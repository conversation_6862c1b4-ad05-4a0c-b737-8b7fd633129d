<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="bookGrad1" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#9C27B0;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#673AB7;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="bookGrad2" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#E91E63;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#AD1457;stop-opacity:1" />
    </linearGradient>
  </defs>

  <!-- 左边打开的书本 -->
  <g transform="translate(2, 8)">
    <!-- 左页 -->
    <path d="M0 0L6 0L6 8L3 7L0 8Z" fill="url(#bookGrad1)"/>
    <!-- 右页 -->
    <path d="M6 0L12 0L12 8L9 7L6 8Z" fill="url(#bookGrad2)"/>
    <!-- 书脊 -->
    <line x1="6" y1="0" x2="6" y2="8" stroke="#4A148C" stroke-width="1"/>
    <!-- 页面内容线条 -->
    <line x1="1" y1="2" x2="5" y2="2" stroke="white" stroke-width="0.3" opacity="0.7"/>
    <line x1="1" y1="3" x2="4" y2="3" stroke="white" stroke-width="0.3" opacity="0.5"/>
    <line x1="7" y1="2" x2="11" y2="2" stroke="white" stroke-width="0.3" opacity="0.7"/>
    <line x1="7" y1="3" x2="10" y2="3" stroke="white" stroke-width="0.3" opacity="0.5"/>
  </g>

  <!-- 合并箭头 -->
  <path d="M16 12L20 12" stroke="#9C27B0" stroke-width="2" stroke-linecap="round"/>
  <path d="M18 10L20 12L18 14" stroke="#9C27B0" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" fill="none"/>

  <!-- 右边合起来的书本 -->
  <g transform="translate(20, 6)">
    <!-- 书本封面 -->
    <rect x="0" y="0" width="3" height="12" rx="0.3" fill="url(#bookGrad1)" stroke="#4A148C" stroke-width="0.5"/>
    <!-- 书脊装饰 -->
    <rect x="0.5" y="2" width="2" height="0.5" fill="white" opacity="0.8"/>
    <rect x="0.5" y="4" width="1.5" height="0.3" fill="white" opacity="0.6"/>
    <rect x="0.5" y="6" width="2" height="0.3" fill="white" opacity="0.8"/>
    <!-- 书本厚度效果 -->
    <path d="M3 0L3.5 0.5L3.5 12.5L3 12Z" fill="#4A148C" opacity="0.7"/>
  </g>
</svg>
