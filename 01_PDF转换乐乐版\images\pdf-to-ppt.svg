<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="pdfGrad" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#ff6b6b;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#ee5a52;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="pptGrad" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#fd79a8;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#fdcb6e;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="arrowGrad" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#a78bfa;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#8b5cf6;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <!-- PDF文件 -->
  <g transform="translate(2, 6)">
    <rect x="0" y="0" width="6" height="8" rx="1" fill="url(#pdfGrad)" opacity="0.9"/>
    <path d="M4 0L4 2L6 2" fill="url(#pdfGrad)" opacity="0.7"/>
    <rect x="1" y="3" width="3" height="0.5" rx="0.25" fill="white" opacity="0.8"/>
    <rect x="1" y="4.5" width="2" height="0.5" rx="0.25" fill="white" opacity="0.6"/>
    <rect x="1" y="6" width="3" height="0.5" rx="0.25" fill="white" opacity="0.8"/>
    <text x="3" y="7.5" text-anchor="middle" fill="white" font-size="2" font-weight="bold">PDF</text>
  </g>
  
  <!-- 转换箭头 -->
  <g transform="translate(12, 12)">
    <circle cx="0" cy="0" r="3" fill="url(#arrowGrad)" opacity="0.1"/>
    <path d="M-2 0L1 0" stroke="url(#arrowGrad)" stroke-width="1.5" stroke-linecap="round"/>
    <path d="M-0.5 -1L1 0L-0.5 1" stroke="url(#arrowGrad)" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" fill="none"/>
  </g>
  
  <!-- PowerPoint文件 -->
  <g transform="translate(16, 6)">
    <rect x="0" y="0" width="6" height="8" rx="1" fill="url(#pptGrad)" opacity="0.9"/>
    <path d="M4 0L4 2L6 2" fill="url(#pptGrad)" opacity="0.7"/>
    
    <!-- PPT幻灯片内容 -->
    <g opacity="0.9">
      <!-- 标题区域 -->
      <rect x="1" y="2.5" width="3.5" height="0.8" rx="0.2" fill="white" opacity="0.9"/>
      
      <!-- 内容区域 -->
      <rect x="1" y="4" width="1.5" height="1.5" rx="0.2" fill="white" opacity="0.7"/>
      <rect x="3" y="4" width="2" height="0.4" rx="0.2" fill="white" opacity="0.8"/>
      <rect x="3" y="4.6" width="1.5" height="0.4" rx="0.2" fill="white" opacity="0.6"/>
      <rect x="3" y="5.2" width="2" height="0.4" rx="0.2" fill="white" opacity="0.8"/>
      
      <!-- 装饰性图表 -->
      <circle cx="1.8" cy="4.8" r="0.3" fill="url(#pptGrad)" opacity="0.5"/>
    </g>
    
    <text x="3" y="7.5" text-anchor="middle" fill="white" font-size="1.5" font-weight="bold">PPT</text>
  </g>
  
  <!-- 幻灯片转换效果 -->
  <g transform="translate(10, 8)">
    <rect x="0" y="0" width="1" height="0.8" rx="0.1" fill="url(#arrowGrad)" opacity="0.6"/>
    <rect x="1.2" y="0.2" width="1" height="0.8" rx="0.1" fill="url(#arrowGrad)" opacity="0.4"/>
    <rect x="2.4" y="0.4" width="1" height="0.8" rx="0.1" fill="url(#arrowGrad)" opacity="0.3"/>
  </g>
  
  <!-- 装饰性元素 -->
  <circle cx="8" cy="4" r="0.3" fill="url(#pdfGrad)" opacity="0.3"/>
  <circle cx="16" cy="16" r="0.3" fill="url(#pptGrad)" opacity="0.3"/>
</svg>
