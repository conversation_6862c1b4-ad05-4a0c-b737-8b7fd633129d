<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="pdfGrad" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#ff6b6b;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#ee5a52;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="imageGrad1" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#a8edea;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#fed6e3;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="imageGrad2" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#ffecd2;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#fcb69f;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="arrowGrad" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#a78bfa;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#8b5cf6;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <!-- PDF文件 -->
  <g transform="translate(2, 6)">
    <rect x="0" y="0" width="6" height="8" rx="1" fill="url(#pdfGrad)" opacity="0.9"/>
    <path d="M4 0L4 2L6 2" fill="url(#pdfGrad)" opacity="0.7"/>
    <rect x="1" y="3" width="3" height="0.5" rx="0.25" fill="white" opacity="0.8"/>
    <rect x="1" y="4.5" width="2" height="0.5" rx="0.25" fill="white" opacity="0.6"/>
    <rect x="1" y="6" width="3" height="0.5" rx="0.25" fill="white" opacity="0.8"/>
    <text x="3" y="7.5" text-anchor="middle" fill="white" font-size="2" font-weight="bold">PDF</text>
  </g>
  
  <!-- 转换箭头 -->
  <g transform="translate(12, 12)">
    <circle cx="0" cy="0" r="3" fill="url(#arrowGrad)" opacity="0.1"/>
    <path d="M-2 0L1 0" stroke="url(#arrowGrad)" stroke-width="1.5" stroke-linecap="round"/>
    <path d="M-0.5 -1L1 0L-0.5 1" stroke="url(#arrowGrad)" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" fill="none"/>
  </g>
  
  <!-- 第一张图片 -->
  <g transform="translate(16, 4)">
    <rect x="0" y="0" width="5" height="4" rx="0.5" fill="url(#imageGrad1)" opacity="0.9"/>
    <circle cx="1.2" cy="1.2" r="0.4" fill="white" opacity="0.9"/>
    <path d="M0 3l1-1 0.8 0.8 1.2-1.2" stroke="white" stroke-width="0.4" stroke-linecap="round" stroke-linejoin="round" fill="none" opacity="0.8"/>
  </g>
  
  <!-- 第二张图片 -->
  <g transform="translate(17, 9)">
    <rect x="0" y="0" width="5" height="4" rx="0.5" fill="url(#imageGrad2)" opacity="0.85"/>
    <circle cx="1.2" cy="1.2" r="0.4" fill="white" opacity="0.9"/>
    <path d="M0 3l1-1 0.8 0.8 1.2-1.2" stroke="white" stroke-width="0.4" stroke-linecap="round" stroke-linejoin="round" fill="none" opacity="0.8"/>
  </g>
  
  <!-- 第三张图片 -->
  <g transform="translate(18, 14)">
    <rect x="0" y="0" width="5" height="4" rx="0.5" fill="url(#imageGrad1)" opacity="0.8"/>
    <circle cx="1.2" cy="1.2" r="0.4" fill="white" opacity="0.9"/>
    <path d="M0 3l1-1 0.8 0.8 1.2-1.2" stroke="white" stroke-width="0.4" stroke-linecap="round" stroke-linejoin="round" fill="none" opacity="0.8"/>
  </g>
  
  <!-- 图片格式标识 -->
  <g transform="translate(14, 20)">
    <rect x="0" y="0" width="8" height="2" rx="0.5" fill="url(#arrowGrad)" opacity="0.2"/>
    <text x="4" y="1.3" text-anchor="middle" fill="url(#arrowGrad)" font-size="1.2" font-weight="bold">JPG/PNG</text>
  </g>
  
  <!-- 转换效果粒子 -->
  <g transform="translate(10, 8)">
    <circle cx="0" cy="0" r="0.2" fill="url(#arrowGrad)" opacity="0.6"/>
    <circle cx="1" cy="1" r="0.15" fill="url(#arrowGrad)" opacity="0.4"/>
    <circle cx="2" cy="0.5" r="0.15" fill="url(#arrowGrad)" opacity="0.5"/>
    <circle cx="0.5" cy="2" r="0.1" fill="url(#arrowGrad)" opacity="0.3"/>
  </g>
  
  <!-- 装饰性元素 -->
  <circle cx="8" cy="4" r="0.3" fill="url(#pdfGrad)" opacity="0.3"/>
  <circle cx="20" cy="20" r="0.3" fill="url(#imageGrad1)" opacity="0.3"/>
</svg>
