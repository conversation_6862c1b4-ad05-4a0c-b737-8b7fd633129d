<svg width="48" height="48" viewBox="0 0 48 48" fill="none" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <!-- 图片渐变 -->
    <linearGradient id="imageGrad" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#4ade80;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#22c55e;stop-opacity:1" />
    </linearGradient>

    <!-- PDF渐变 -->
    <linearGradient id="pdfGrad" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#ef4444;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#dc2626;stop-opacity:1" />
    </linearGradient>

    <!-- 箭头渐变 -->
    <linearGradient id="arrowGrad" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#8b5cf6;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#7c3aed;stop-opacity:1" />
    </linearGradient>

    <!-- 背景渐变 -->
    <linearGradient id="bgGrad" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#f8fafc;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#e2e8f0;stop-opacity:1" />
    </linearGradient>
  </defs>

  <!-- 背景圆形 -->
  <circle cx="24" cy="24" r="22" fill="url(#bgGrad)" stroke="#e2e8f0" stroke-width="1"/>

  <!-- 左侧图片图标 -->
  <g transform="translate(6, 14)">
    <!-- 图片框架 -->
    <rect x="0" y="0" width="14" height="12" rx="2" fill="url(#imageGrad)" stroke="#16a34a" stroke-width="1"/>

    <!-- 山峰图标 -->
    <path d="M2 8 L5 5 L8 7 L12 3" stroke="white" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" fill="none"/>

    <!-- 太阳 -->
    <circle cx="10" cy="4" r="1.5" fill="white" opacity="0.9"/>

    <!-- 图片标识 -->
    <text x="7" y="16" text-anchor="middle" fill="#16a34a" font-size="3" font-weight="bold">JPG</text>
  </g>

  <!-- 转换箭头 -->
  <g transform="translate(24, 24)">
    <circle cx="0" cy="0" r="4" fill="url(#arrowGrad)" opacity="0.1"/>
    <path d="M-3 0 L3 0" stroke="url(#arrowGrad)" stroke-width="2" stroke-linecap="round"/>
    <path d="M1 -2 L3 0 L1 2" stroke="url(#arrowGrad)" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" fill="none"/>
  </g>

  <!-- 右侧PDF文件 -->
  <g transform="translate(28, 12)">
    <!-- PDF文件主体 -->
    <rect x="0" y="0" width="14" height="16" rx="2" fill="url(#pdfGrad)" stroke="#dc2626" stroke-width="1"/>

    <!-- 文件折角 -->
    <path d="M10 0 L10 3 L14 3" fill="none" stroke="#dc2626" stroke-width="1"/>
    <path d="M10 0 L10 3 L14 3 Z" fill="#b91c1c" opacity="0.7"/>

    <!-- PDF内容线条 -->
    <rect x="2" y="5" width="8" height="1" rx="0.5" fill="white" opacity="0.9"/>
    <rect x="2" y="7" width="6" height="1" rx="0.5" fill="white" opacity="0.7"/>
    <rect x="2" y="9" width="8" height="1" rx="0.5" fill="white" opacity="0.9"/>
    <rect x="2" y="11" width="5" height="1" rx="0.5" fill="white" opacity="0.7"/>

    <!-- PDF标识 -->
    <text x="7" y="15" text-anchor="middle" fill="white" font-size="3" font-weight="bold">PDF</text>

    <!-- PDF标识下方 -->
    <text x="7" y="20" text-anchor="middle" fill="#dc2626" font-size="3" font-weight="bold">PDF</text>
  </g>

  <!-- 装饰性元素 -->
  <circle cx="12" cy="8" r="1" fill="url(#imageGrad)" opacity="0.3"/>
  <circle cx="36" cy="36" r="1" fill="url(#pdfGrad)" opacity="0.3"/>
  <circle cx="40" cy="8" r="0.5" fill="url(#arrowGrad)" opacity="0.4"/>
</svg>
