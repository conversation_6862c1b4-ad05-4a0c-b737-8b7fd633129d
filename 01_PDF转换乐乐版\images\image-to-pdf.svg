<svg width="48" height="48" viewBox="0 0 48 48" fill="none" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="imageGrad1" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#a8edea;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#fed6e3;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="imageGrad2" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#ffecd2;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#fcb69f;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="imageGrad3" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#a8e6cf;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#88d8a3;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="pdfGrad" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#ff6b6b;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#ee5a52;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="arrowGrad" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#a78bfa;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#8b5cf6;stop-opacity:1" />
    </linearGradient>
  </defs>

  <!-- 背景圆形 -->
  <circle cx="24" cy="24" r="22" fill="url(#arrowGrad)" opacity="0.05"/>

  <!-- 第一张图片 -->
  <g transform="translate(4, 8)">
    <rect x="0" y="0" width="10" height="8" rx="1.5" fill="url(#imageGrad1)" opacity="0.95"/>
    <circle cx="2.5" cy="2.5" r="1" fill="white" opacity="0.9"/>
    <path d="M0 6l2-2 1.5 1.5 2.5-2.5" stroke="white" stroke-width="1" stroke-linecap="round" stroke-linejoin="round" fill="none" opacity="0.8"/>
  </g>

  <!-- 第二张图片 -->
  <g transform="translate(6, 12)">
    <rect x="0" y="0" width="10" height="8" rx="1.5" fill="url(#imageGrad2)" opacity="0.9"/>
    <circle cx="2.5" cy="2.5" r="1" fill="white" opacity="0.9"/>
    <path d="M0 6l2-2 1.5 1.5 2.5-2.5" stroke="white" stroke-width="1" stroke-linecap="round" stroke-linejoin="round" fill="none" opacity="0.8"/>
  </g>

  <!-- 第三张图片 -->
  <g transform="translate(8, 16)">
    <rect x="0" y="0" width="10" height="8" rx="1.5" fill="url(#imageGrad3)" opacity="0.85"/>
    <circle cx="2.5" cy="2.5" r="1" fill="white" opacity="0.9"/>
    <path d="M0 6l2-2 1.5 1.5 2.5-2.5" stroke="white" stroke-width="1" stroke-linecap="round" stroke-linejoin="round" fill="none" opacity="0.8"/>
  </g>

  <!-- 转换箭头 -->
  <g transform="translate(24, 24)">
    <circle cx="0" cy="0" r="6" fill="url(#arrowGrad)" opacity="0.15"/>
    <path d="M-4 0L2 0" stroke="url(#arrowGrad)" stroke-width="2" stroke-linecap="round"/>
    <path d="M-1 -2L2 0L-1 2" stroke="url(#arrowGrad)" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" fill="none"/>
  </g>

  <!-- PDF文件 -->
  <g transform="translate(30, 10)">
    <rect x="0" y="0" width="14" height="18" rx="2" fill="url(#pdfGrad)" opacity="0.9"/>
    <path d="M10 0L10 4L14 4" fill="url(#pdfGrad)" opacity="0.7"/>

    <!-- PDF内容预览 -->
    <rect x="2" y="6" width="8" height="1" rx="0.5" fill="white" opacity="0.9"/>
    <rect x="2" y="8" width="6" height="1" rx="0.5" fill="white" opacity="0.7"/>
    <rect x="2" y="10" width="8" height="1" rx="0.5" fill="white" opacity="0.9"/>
    <rect x="2" y="12" width="5" height="1" rx="0.5" fill="white" opacity="0.7"/>

    <text x="7" y="16" text-anchor="middle" fill="white" font-size="4" font-weight="bold">PDF</text>
  </g>

  <!-- 图片数量标识 -->
  <g transform="translate(12, 28)">
    <circle cx="0" cy="0" r="3" fill="url(#arrowGrad)" opacity="0.9"/>
    <text x="0" y="1" text-anchor="middle" fill="white" font-size="4" font-weight="bold">3</text>
  </g>

  <!-- 装饰性元素 -->
  <circle cx="20" cy="12" r="0.5" fill="url(#imageGrad1)" opacity="0.4"/>
  <circle cx="32" cy="32" r="0.5" fill="url(#pdfGrad)" opacity="0.4"/>
</svg>
