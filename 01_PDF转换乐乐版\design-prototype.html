<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>PDF转换工具 - 首页设计原型</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
            background-color: #f8f9fa;
            color: #333;
        }
        
        .container {
            max-width: 375px;
            margin: 0 auto;
            background: white;
            min-height: 100vh;
        }
        
        /* 导航栏 */
        .navbar {
            height: 44px;
            background: white;
            display: flex;
            align-items: center;
            justify-content: center;
            border-bottom: 1px solid #e5e5e5;
            position: sticky;
            top: 0;
            z-index: 100;
        }
        
        .navbar-title {
            font-size: 17px;
            font-weight: 500;
            color: #000;
        }
        
        /* 搜索栏 */
        .search-section {
            padding: 16px;
            background: white;
        }
        
        .search-box {
            background: #f5f5f5;
            border-radius: 20px;
            padding: 10px 16px;
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .search-icon {
            color: #999;
            font-size: 14px;
        }
        
        .search-input {
            border: none;
            background: none;
            outline: none;
            flex: 1;
            font-size: 14px;
            color: #333;
        }
        
        /* 热门功能区 */
        .hot-features {
            padding: 0 16px 16px;
            background: white;
        }
        
        .section-title {
            font-size: 16px;
            font-weight: 600;
            margin-bottom: 12px;
            color: #333;
            display: flex;
            align-items: center;
            gap: 6px;
        }
        
        .feature-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 12px;
        }
        
        .feature-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 12px;
            padding: 16px;
            color: white;
            text-decoration: none;
            position: relative;
            overflow: hidden;
            min-height: 80px;
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .feature-card .feature-icon {
            flex-shrink: 0;
            width: 48px;
            height: 48px;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .feature-card.secondary {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
        }
        
        .feature-card.tertiary {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
        }
        
        .feature-card.quaternary {
            background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
        }
        
        .feature-title {
            font-size: 14px;
            font-weight: 500;
            margin-bottom: 4px;
        }
        
        .feature-desc {
            font-size: 12px;
            opacity: 0.8;
        }
        

        
        /* 全部功能区 */
        .all-features {
            margin-top: 12px;
            background: white;
            border-radius: 12px 12px 0 0;
            padding: 16px;
        }
        
        .feature-list {
            list-style: none;
        }
        
        .feature-item {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 12px 0;
            border-bottom: 1px solid #f0f0f0;
            text-decoration: none;
            color: #333;
        }
        
        .feature-item:last-child {
            border-bottom: none;
        }
        
        .feature-left {
            display: flex;
            align-items: center;
            gap: 12px;
        }
        
        .feature-icon {
            width: 32px;
            height: 32px;
            background: #f5f5f5;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .feature-icon svg {
            width: 18px;
            height: 18px;
            stroke: #666;
            fill: none;
            stroke-width: 2;
            stroke-linecap: round;
            stroke-linejoin: round;
        }
        
        .feature-info h3 {
            font-size: 14px;
            font-weight: 500;
            margin-bottom: 2px;
        }
        
        .feature-info p {
            font-size: 12px;
            color: #666;
        }
        
        .feature-right {
            display: flex;
            align-items: center;
        }
        

        
        .arrow {
            color: #ccc;
            font-size: 12px;
        }
        
        /* 底部间距 */
        .bottom-space {
            height: 20px;
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- 导航栏 -->
        <div class="navbar">
            <div class="navbar-title">PDF转换工具</div>
        </div>
        
        <!-- 搜索栏 -->
        <div class="search-section">
            <div class="search-box">
                <span class="search-icon">
                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                        <circle cx="11" cy="11" r="8"/>
                        <path d="m21 21-4.35-4.35"/>
                    </svg>
                </span>
                <input type="text" class="search-input" placeholder="搜索PDF功能...">
            </div>
        </div>
        
        <!-- 热门功能 -->
        <div class="hot-features">
            <h2 class="section-title">
                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" style="color: #ff6b35;">
                    <path d="M8.5 14.5A2.5 2.5 0 0011 12c0-1.38-.5-2-1-3-1.072-2.143-.224-4.054 2-6 .5 2.5 2 4.9 4 6.5 2 1.6 3 3.5 3 5.5a7 7 0 11-14 0c0-1.153.433-2.294 1-3a2.5 2.5 0 002.5 2.5z"/>
                </svg>
                热门功能
            </h2>
            <div class="feature-grid">
                <a href="#" class="feature-card">
                    <div class="feature-icon">
                        <svg width="48" height="48" viewBox="0 0 48 48" fill="none">
                            <defs>
                                <linearGradient id="compressGrad" x1="0%" y1="0%" x2="100%" y2="100%">
                                    <stop offset="0%" style="stop-color:#FFD700;stop-opacity:1" />
                                    <stop offset="100%" style="stop-color:#FFA500;stop-opacity:1" />
                                </linearGradient>
                            </defs>
                            <!-- 大文件 -->
                            <rect x="8" y="6" width="18" height="24" rx="2" fill="url(#compressGrad)" opacity="0.8"/>
                            <path d="M12 10h10M12 14h8M12 18h10M12 22h6" stroke="white" stroke-width="1.5" stroke-linecap="round"/>
                            <!-- 压缩箭头 -->
                            <path d="M30 16L36 20L30 24" stroke="white" stroke-width="2.5" stroke-linecap="round" stroke-linejoin="round" fill="none"/>
                            <!-- 小文件 -->
                            <rect x="38" y="12" width="8" height="12" rx="1" fill="url(#compressGrad)"/>
                            <path d="M40 16h4M40 18h3M40 20h4" stroke="white" stroke-width="1" stroke-linecap="round"/>
                        </svg>
                    </div>
                    <div>
                        <div class="feature-title">PDF压缩</div>
                        <div class="feature-desc">减小文件大小</div>
                    </div>
                </a>

                <a href="#" class="feature-card secondary">
                    <div class="feature-icon">
                        <svg width="48" height="48" viewBox="0 0 48 48" fill="none">
                            <defs>
                                <linearGradient id="wordGrad" x1="0%" y1="0%" x2="100%" y2="100%">
                                    <stop offset="0%" style="stop-color:#E53E3E;stop-opacity:1" />
                                    <stop offset="100%" style="stop-color:#C53030;stop-opacity:1" />
                                </linearGradient>
                                <linearGradient id="blueGrad" x1="0%" y1="0%" x2="100%" y2="100%">
                                    <stop offset="0%" style="stop-color:#4FC3F7;stop-opacity:1" />
                                    <stop offset="100%" style="stop-color:#29B6F6;stop-opacity:1" />
                                </linearGradient>
                            </defs>
                            <!-- PDF文件 -->
                            <rect x="6" y="6" width="16" height="20" rx="2" fill="url(#wordGrad)" opacity="0.9"/>
                            <text x="14" y="18" text-anchor="middle" fill="white" font-size="8" font-weight="bold">PDF</text>
                            <!-- 转换箭头 -->
                            <path d="M26 16L32 16" stroke="#666" stroke-width="2.5" stroke-linecap="round"/>
                            <path d="M29 13L32 16L29 19" stroke="#666" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" fill="none"/>
                            <!-- Word文件 -->
                            <rect x="36" y="6" width="16" height="20" rx="2" fill="url(#blueGrad)" opacity="0.9"/>
                            <text x="44" y="18" text-anchor="middle" fill="white" font-size="7" font-weight="bold">WORD</text>
                        </svg>
                    </div>
                    <div>
                        <div class="feature-title">PDF转Word</div>
                        <div class="feature-desc">可编辑文档</div>
                    </div>
                </a>

                <a href="#" class="feature-card tertiary">
                    <div class="feature-icon">
                        <svg width="48" height="48" viewBox="0 0 48 48" fill="none">
                            <defs>
                                <linearGradient id="unlockGrad" x1="0%" y1="0%" x2="100%" y2="100%">
                                    <stop offset="0%" style="stop-color:#FF6B6B;stop-opacity:1" />
                                    <stop offset="100%" style="stop-color:#FF8E53;stop-opacity:1" />
                                </linearGradient>
                            </defs>
                            <!-- 锁住的PDF -->
                            <rect x="8" y="12" width="14" height="18" rx="2" fill="#E0E0E0" opacity="0.8"/>
                            <rect x="12" y="20" width="6" height="6" rx="1" fill="url(#unlockGrad)"/>
                            <circle cx="15" cy="23" r="1.5" fill="white"/>
                            <path d="M14 12V8a4 4 0 018 0v4" stroke="url(#unlockGrad)" stroke-width="2.5" stroke-linecap="round" fill="none"/>
                            <!-- 解密箭头和钥匙 -->
                            <path d="M26 24L32 24" stroke="#4CAF50" stroke-width="2.5" stroke-linecap="round"/>
                            <circle cx="34" cy="24" r="2" fill="#4CAF50"/>
                            <path d="M36 24h4" stroke="#4CAF50" stroke-width="2" stroke-linecap="round"/>
                            <path d="M38 22v4" stroke="#4CAF50" stroke-width="1.5" stroke-linecap="round"/>
                            <!-- 解锁的PDF -->
                            <rect x="42" y="12" width="14" height="18" rx="2" fill="url(#unlockGrad)" opacity="0.9"/>
                            <text x="49" y="23" text-anchor="middle" fill="white" font-size="8" font-weight="bold">PDF</text>
                        </svg>
                    </div>
                    <div>
                        <div class="feature-title">PDF解密</div>
                        <div class="feature-desc">移除密码保护</div>
                    </div>
                </a>

                <a href="#" class="feature-card quaternary">
                    <div class="feature-icon">
                        <svg width="48" height="48" viewBox="0 0 48 48" fill="none">
                            <defs>
                                <linearGradient id="imageGrad" x1="0%" y1="0%" x2="100%" y2="100%">
                                    <stop offset="0%" style="stop-color:#66BB6A;stop-opacity:1" />
                                    <stop offset="100%" style="stop-color:#4CAF50;stop-opacity:1" />
                                </linearGradient>
                                <linearGradient id="pdfGrad" x1="0%" y1="0%" x2="100%" y2="100%">
                                    <stop offset="0%" style="stop-color:#E53E3E;stop-opacity:1" />
                                    <stop offset="100%" style="stop-color:#C53030;stop-opacity:1" />
                                </linearGradient>
                            </defs>
                            <!-- 多张图片 -->
                            <rect x="4" y="8" width="12" height="10" rx="1" fill="url(#imageGrad)" opacity="0.9"/>
                            <circle cx="8" cy="12" r="1.5" fill="white" opacity="0.8"/>
                            <path d="M4 16l3-3 2 2 3-3" stroke="white" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" fill="none"/>

                            <rect x="8" y="12" width="12" height="10" rx="1" fill="url(#imageGrad)" opacity="0.7"/>
                            <circle cx="12" cy="16" r="1.5" fill="white" opacity="0.8"/>

                            <rect x="12" y="16" width="12" height="10" rx="1" fill="url(#imageGrad)" opacity="0.5"/>

                            <!-- 转换箭头 -->
                            <path d="M28 20L34 20" stroke="#666" stroke-width="2.5" stroke-linecap="round"/>
                            <path d="M31 17L34 20L31 23" stroke="#666" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" fill="none"/>

                            <!-- PDF文件 -->
                            <rect x="38" y="12" width="14" height="18" rx="2" fill="url(#pdfGrad)" opacity="0.9"/>
                            <text x="45" y="23" text-anchor="middle" fill="white" font-size="8" font-weight="bold">PDF</text>
                        </svg>
                    </div>
                    <div>
                        <div class="feature-title">图片转PDF</div>
                        <div class="feature-desc">多图合成PDF</div>
                    </div>
                </a>
            </div>
        </div>
        
        <!-- 全部功能 -->
        <div class="all-features">
            <h2 class="section-title">
                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" style="color: #666;">
                    <path d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"/>
                </svg>
                全部功能
            </h2>
            <div class="feature-list">
                <a href="#" class="feature-item">
                    <div class="feature-left">
                        <div class="feature-icon">
                            <svg viewBox="0 0 24 24">
                                <path d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"/>
                                <path d="M9 8h3"/>
                            </svg>
                        </div>
                        <div class="feature-info">
                            <h3>PDF文件合并</h3>
                            <p>多个PDF合成一个</p>
                        </div>
                    </div>
                    <div class="feature-right">
                        <span class="arrow">›</span>
                    </div>
                </a>

                <a href="#" class="feature-item">
                    <div class="feature-left">
                        <div class="feature-icon">
                            <svg viewBox="0 0 24 24">
                                <path d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"/>
                            </svg>
                        </div>
                        <div class="feature-info">
                            <h3>PDF去水印</h3>
                            <p>清除文档水印</p>
                        </div>
                    </div>
                    <div class="feature-right">
                        <span class="arrow">›</span>
                    </div>
                </a>

                <a href="#" class="feature-item">
                    <div class="feature-left">
                        <div class="feature-icon">
                            <svg viewBox="0 0 24 24">
                                <path d="M3 4a1 1 0 011-1h16a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zM3 10a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H4a1 1 0 01-1-1v-6zM14 9a1 1 0 011-1h5a1 1 0 011 1v2a1 1 0 01-1 1h-5a1 1 0 01-1-1V9zM14 15a1 1 0 011-1h5a1 1 0 011 1v2a1 1 0 01-1 1h-5a1 1 0 01-1-1v-2z"/>
                            </svg>
                        </div>
                        <div class="feature-info">
                            <h3>PDF转Excel</h3>
                            <p>转换为表格格式</p>
                        </div>
                    </div>
                    <div class="feature-right">
                        <span class="arrow">›</span>
                    </div>
                </a>

                <a href="#" class="feature-item">
                    <div class="feature-left">
                        <div class="feature-icon">
                            <svg viewBox="0 0 24 24">
                                <path d="M7 4V2a1 1 0 011-1h8a1 1 0 011 1v2"/>
                                <path d="M5 4h14a2 2 0 012 2v10a2 2 0 01-2 2H5a2 2 0 01-2-2V6a2 2 0 012-2z"/>
                                <path d="M9 9h6v6H9z"/>
                            </svg>
                        </div>
                        <div class="feature-info">
                            <h3>PDF转PPT</h3>
                            <p>转换为演示文稿</p>
                        </div>
                    </div>
                    <div class="feature-right">
                        <span class="arrow">›</span>
                    </div>
                </a>

                <a href="#" class="feature-item">
                    <div class="feature-left">
                        <div class="feature-icon">
                            <svg viewBox="0 0 24 24">
                                <path d="M12 3l8 8-8 8-8-8 8-8z"/>
                                <path d="M8.5 10.5L12 7l3.5 3.5L12 14l-3.5-3.5z"/>
                            </svg>
                        </div>
                        <div class="feature-info">
                            <h3>PDF加水印</h3>
                            <p>添加版权保护</p>
                        </div>
                    </div>
                    <div class="feature-right">
                        <span class="arrow">›</span>
                    </div>
                </a>

                <a href="#" class="feature-item">
                    <div class="feature-left">
                        <div class="feature-icon">
                            <svg viewBox="0 0 24 24">
                                <path d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"/>
                            </svg>
                        </div>
                        <div class="feature-info">
                            <h3>PDF转图片</h3>
                            <p>转换为图片格式</p>
                        </div>
                    </div>
                    <div class="feature-right">
                        <span class="arrow">›</span>
                    </div>
                </a>

                <a href="#" class="feature-item">
                    <div class="feature-left">
                        <div class="feature-icon">
                            <svg viewBox="0 0 24 24">
                                <path d="M14.828 14.828a4 4 0 01-5.656 0M9 10h1m4 0h1m-6 4h1m4 0h1"/>
                                <path d="M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"/>
                            </svg>
                        </div>
                        <div class="feature-info">
                            <h3>PDF拆分</h3>
                            <p>分割成多个文件</p>
                        </div>
                    </div>
                    <div class="feature-right">
                        <span class="arrow">›</span>
                    </div>
                </a>
            </div>
        </div>
        
        <div class="bottom-space"></div>
    </div>
</body>
</html>
