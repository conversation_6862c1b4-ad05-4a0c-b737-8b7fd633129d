<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="originalGrad" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#667eea;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#764ba2;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="splitGrad1" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#a8edea;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#fed6e3;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="splitGrad2" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#ffecd2;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#fcb69f;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="arrowGrad" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#a78bfa;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#8b5cf6;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <!-- 原始PDF文件 -->
  <g transform="translate(2, 6)">
    <rect x="0" y="0" width="6" height="8" rx="1" fill="url(#originalGrad)" opacity="0.9"/>
    <path d="M4 0L4 2L6 2" fill="url(#originalGrad)" opacity="0.7"/>
    
    <!-- 多页内容 -->
    <rect x="1" y="2.5" width="3" height="0.4" rx="0.2" fill="white" opacity="0.9"/>
    <rect x="1" y="3.2" width="2" height="0.4" rx="0.2" fill="white" opacity="0.7"/>
    <rect x="1" y="3.9" width="3" height="0.4" rx="0.2" fill="white" opacity="0.9"/>
    
    <!-- 分页线 -->
    <line x1="0.5" y1="4.5" x2="5.5" y2="4.5" stroke="url(#arrowGrad)" stroke-width="0.3" stroke-dasharray="0.5,0.5" opacity="0.6"/>
    
    <rect x="1" y="5" width="3" height="0.4" rx="0.2" fill="white" opacity="0.9"/>
    <rect x="1" y="5.7" width="2.5" height="0.4" rx="0.2" fill="white" opacity="0.7"/>
    <rect x="1" y="6.4" width="3" height="0.4" rx="0.2" fill="white" opacity="0.9"/>
    
    <text x="3" y="7.5" text-anchor="middle" fill="white" font-size="1.5" font-weight="bold">PDF</text>
  </g>
  
  <!-- 拆分箭头 -->
  <g transform="translate(12, 12)">
    <circle cx="0" cy="0" r="3" fill="url(#arrowGrad)" opacity="0.1"/>
    <!-- 向上箭头 -->
    <path d="M-0.5 -1L0 -2L0.5 -1" stroke="url(#arrowGrad)" stroke-width="1.2" stroke-linecap="round" stroke-linejoin="round" fill="none"/>
    <path d="M0 -2L0 0" stroke="url(#arrowGrad)" stroke-width="1.2" stroke-linecap="round"/>
    <!-- 向下箭头 -->
    <path d="M-0.5 1L0 2L0.5 1" stroke="url(#arrowGrad)" stroke-width="1.2" stroke-linecap="round" stroke-linejoin="round" fill="none"/>
    <path d="M0 2L0 0" stroke="url(#arrowGrad)" stroke-width="1.2" stroke-linecap="round"/>
  </g>
  
  <!-- 拆分后的第一个PDF -->
  <g transform="translate(16, 4)">
    <rect x="0" y="0" width="5" height="6" rx="0.8" fill="url(#splitGrad1)" opacity="0.9"/>
    <path d="M3.5" y="0" width="1.5" height="1.5" rx="0.3" fill="url(#splitGrad1)" opacity="0.7"/>
    <rect x="0.8" y="2" width="2.5" height="0.3" rx="0.15" fill="white" opacity="0.9"/>
    <rect x="0.8" y="2.6" width="1.8" height="0.3" rx="0.15" fill="white" opacity="0.7"/>
    <rect x="0.8" y="3.2" width="2.5" height="0.3" rx="0.15" fill="white" opacity="0.9"/>
    <text x="2.5" y="5.2" text-anchor="middle" fill="white" font-size="1.2" font-weight="bold">1</text>
  </g>
  
  <!-- 拆分后的第二个PDF -->
  <g transform="translate(16, 14)">
    <rect x="0" y="0" width="5" height="6" rx="0.8" fill="url(#splitGrad2)" opacity="0.9"/>
    <path d="M3.5 0L3.5 1.5L5 1.5" fill="url(#splitGrad2)" opacity="0.7"/>
    <rect x="0.8" y="2" width="2.5" height="0.3" rx="0.15" fill="white" opacity="0.9"/>
    <rect x="0.8" y="2.6" width="2" height="0.3" rx="0.15" fill="white" opacity="0.7"/>
    <rect x="0.8" y="3.2" width="2.5" height="0.3" rx="0.15" fill="white" opacity="0.9"/>
    <text x="2.5" y="5.2" text-anchor="middle" fill="white" font-size="1.2" font-weight="bold">2</text>
  </g>
  
  <!-- 拆分效果 -->
  <g transform="translate(10, 8)">
    <circle cx="0" cy="0" r="0.2" fill="url(#arrowGrad)" opacity="0.6"/>
    <circle cx="1" cy="-1" r="0.15" fill="url(#arrowGrad)" opacity="0.4"/>
    <circle cx="1" cy="1" r="0.15" fill="url(#arrowGrad)" opacity="0.4"/>
    <circle cx="2" cy="0" r="0.1" fill="url(#arrowGrad)" opacity="0.3"/>
  </g>
  
  <!-- 装饰性元素 -->
  <circle cx="8" cy="4" r="0.3" fill="url(#originalGrad)" opacity="0.3"/>
  <circle cx="18" cy="2" r="0.2" fill="url(#splitGrad1)" opacity="0.3"/>
  <circle cx="18" cy="22" r="0.2" fill="url(#splitGrad2)" opacity="0.3"/>
</svg>
