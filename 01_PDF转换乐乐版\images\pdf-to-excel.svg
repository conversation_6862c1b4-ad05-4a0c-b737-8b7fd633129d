<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="pdfGrad" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#ff6b6b;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#ee5a52;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="excelGrad" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#00b894;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#00cec9;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="arrowGrad" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#a78bfa;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#8b5cf6;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <!-- PDF文件 -->
  <g transform="translate(2, 6)">
    <rect x="0" y="0" width="6" height="8" rx="1" fill="url(#pdfGrad)" opacity="0.9"/>
    <path d="M4 0L4 2L6 2" fill="url(#pdfGrad)" opacity="0.7"/>
    <rect x="1" y="3" width="3" height="0.5" rx="0.25" fill="white" opacity="0.8"/>
    <rect x="1" y="4.5" width="2" height="0.5" rx="0.25" fill="white" opacity="0.6"/>
    <rect x="1" y="6" width="3" height="0.5" rx="0.25" fill="white" opacity="0.8"/>
    <text x="3" y="7.5" text-anchor="middle" fill="white" font-size="2" font-weight="bold">PDF</text>
  </g>
  
  <!-- 转换箭头 -->
  <g transform="translate(12, 12)">
    <circle cx="0" cy="0" r="3" fill="url(#arrowGrad)" opacity="0.1"/>
    <path d="M-2 0L1 0" stroke="url(#arrowGrad)" stroke-width="1.5" stroke-linecap="round"/>
    <path d="M-0.5 -1L1 0L-0.5 1" stroke="url(#arrowGrad)" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" fill="none"/>
  </g>
  
  <!-- Excel文件 -->
  <g transform="translate(16, 6)">
    <rect x="0" y="0" width="6" height="8" rx="1" fill="url(#excelGrad)" opacity="0.9"/>
    <path d="M4 0L4 2L6 2" fill="url(#excelGrad)" opacity="0.7"/>
    
    <!-- Excel表格网格 -->
    <g opacity="0.9">
      <!-- 水平线 -->
      <line x1="1" y1="3" x2="5" y2="3" stroke="white" stroke-width="0.3"/>
      <line x1="1" y1="4" x2="5" y2="4" stroke="white" stroke-width="0.3"/>
      <line x1="1" y1="5" x2="5" y2="5" stroke="white" stroke-width="0.3"/>
      <line x1="1" y1="6" x2="5" y2="6" stroke="white" stroke-width="0.3"/>
      
      <!-- 垂直线 -->
      <line x1="2" y1="2.5" x2="2" y2="6.5" stroke="white" stroke-width="0.3"/>
      <line x1="3" y1="2.5" x2="3" y2="6.5" stroke="white" stroke-width="0.3"/>
      <line x1="4" y1="2.5" x2="4" y2="6.5" stroke="white" stroke-width="0.3"/>
      
      <!-- 表格数据点 -->
      <circle cx="1.5" cy="3.5" r="0.2" fill="white" opacity="0.8"/>
      <circle cx="2.5" cy="3.5" r="0.2" fill="white" opacity="0.8"/>
      <circle cx="3.5" cy="3.5" r="0.2" fill="white" opacity="0.8"/>
      <circle cx="1.5" cy="4.5" r="0.2" fill="white" opacity="0.6"/>
      <circle cx="2.5" cy="4.5" r="0.2" fill="white" opacity="0.6"/>
      <circle cx="1.5" cy="5.5" r="0.2" fill="white" opacity="0.8"/>
    </g>
    
    <text x="3" y="7.5" text-anchor="middle" fill="white" font-size="1.5" font-weight="bold">XLS</text>
  </g>
  
  <!-- 数据转换效果 -->
  <g transform="translate(10, 8)">
    <circle cx="0" cy="0" r="0.3" fill="url(#arrowGrad)" opacity="0.6"/>
    <circle cx="1" cy="1" r="0.2" fill="url(#arrowGrad)" opacity="0.4"/>
    <circle cx="2" cy="0.5" r="0.2" fill="url(#arrowGrad)" opacity="0.5"/>
  </g>
  
  <!-- 装饰性元素 -->
  <circle cx="8" cy="4" r="0.3" fill="url(#pdfGrad)" opacity="0.3"/>
  <circle cx="16" cy="16" r="0.3" fill="url(#excelGrad)" opacity="0.3"/>
</svg>
